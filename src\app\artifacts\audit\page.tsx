"use client"

import * as React from "react"
import { ArtifactsSearch } from "@/components/artifacts/artifacts-search"
import { ArtifactsFilters } from "@/components/artifacts/artifacts-filters"
import { ArtifactsMetrics } from "@/components/artifacts/artifacts-metrics"
import { ArtifactsBulkActions } from "@/components/artifacts/artifacts-bulk-actions"
import { ArtifactsTable } from "@/components/artifacts/artifacts-table"
import { Artifact, ArtifactFilter, ArtifactSort } from "@/lib/artifacts-types"
import { sampleArtifacts, storageMetrics } from "@/lib/artifacts-data"

export default function AuditReadyEvidencePage() {
  const [searchQuery, setSearchQuery] = React.useState("")
  const [filters, setFilters] = React.useState<ArtifactFilter>({
    blockchainStatus: ["verified"],
    artifactTypes: ["audit-evidence", "control-evidence", "assessment-report"]
  })
  const [selectedArtifacts, setSelectedArtifacts] = React.useState<Artifact[]>([])
  const [sort, setSort] = React.useState<ArtifactSort>({ field: "createdAt", direction: "desc" })

  // Filter artifacts to show audit-ready evidence
  const auditArtifacts = React.useMemo(() => {
    return sampleArtifacts.filter(artifact =>
      artifact.blockchainStatus === "verified" &&
      ["audit-evidence", "control-evidence", "assessment-report", "compliance-certificate"].includes(artifact.artifactType)
    )
  }, [])

  // Filter and sort artifacts
  const filteredArtifacts = React.useMemo(() => {
    let filtered = auditArtifacts.filter(artifact => {
      // Search filter
      if (searchQuery) {
        const searchLower = searchQuery.toLowerCase()
        const matchesSearch =
          artifact.name.toLowerCase().includes(searchLower) ||
          artifact.description?.toLowerCase().includes(searchLower) ||
          artifact.tags.some(tag => tag.toLowerCase().includes(searchLower)) ||
          artifact.createdBy.toLowerCase().includes(searchLower)

        if (!matchesSearch) return false
      }

      // Apply other filters...
      if (filters.artifactTypes?.length) {
        if (!filters.artifactTypes.includes(artifact.artifactType)) return false
      }

      if (filters.sourceModules?.length) {
        if (!filters.sourceModules.includes(artifact.sourceModule)) return false
      }

      if (filters.blockchainStatus?.length) {
        if (!filters.blockchainStatus.includes(artifact.blockchainStatus)) return false
      }

      if (filters.accessLevels?.length) {
        if (!filters.accessLevels.includes(artifact.accessLevel)) return false
      }

      if (filters.dateRange) {
        const artifactDate = artifact.createdAt
        if (artifactDate < filters.dateRange.start || artifactDate > filters.dateRange.end) {
          return false
        }
      }

      if (filters.isArchived !== undefined) {
        if (artifact.isArchived !== filters.isArchived) return false
      }

      return true
    })

    // Sort artifacts
    filtered.sort((a, b) => {
      const aValue = a[sort.field]
      const bValue = b[sort.field]

      if (aValue < bValue) return sort.direction === "asc" ? -1 : 1
      if (aValue > bValue) return sort.direction === "asc" ? 1 : -1
      return 0
    })

    return filtered
  }, [auditArtifacts, searchQuery, filters, sort])

  const handleSaveSearch = (name: string) => {
    console.log("Saving search:", name, { searchQuery, filters })
  }

  const handleViewArtifact = (artifact: Artifact) => {
    console.log("Viewing artifact:", artifact.id)
  }

  const handleDownloadArtifact = (artifact: Artifact) => {
    console.log("Downloading artifact:", artifact.id)
  }

  const handleVerifyArtifact = (artifact: Artifact) => {
    console.log("Verifying artifact:", artifact.id)
  }

  const handleBulkExport = (artifacts: Artifact[]) => {
    console.log("Bulk exporting:", artifacts.length, "artifacts")
  }

  const handleBulkShare = (artifacts: Artifact[]) => {
    console.log("Bulk sharing:", artifacts.length, "artifacts")
  }

  const handleBulkVerify = (artifacts: Artifact[]) => {
    console.log("Bulk verifying:", artifacts.length, "artifacts")
  }

  const handleBulkArchive = (artifacts: Artifact[]) => {
    console.log("Bulk archiving:", artifacts.length, "artifacts")
  }

  const handleBulkDelete = (artifacts: Artifact[]) => {
    console.log("Bulk deleting:", artifacts.length, "artifacts")
  }

  const handleCreateEvidencePackage = (artifacts: Artifact[]) => {
    console.log("Creating evidence package for:", artifacts.length, "artifacts")
  }

  return (
    <div className="space-y-6">
      {/* Storage Metrics Dashboard */}
      <ArtifactsMetrics metrics={storageMetrics} />

      {/* Search Interface */}
      <ArtifactsSearch
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        onSaveSearch={handleSaveSearch}
      />

      {/* Filters */}
      <ArtifactsFilters
        filters={filters}
        onFiltersChange={setFilters}
      />

      {/* Bulk Actions */}
      <ArtifactsBulkActions
        selectedArtifacts={selectedArtifacts}
        onClearSelection={() => setSelectedArtifacts([])}
        onBulkExport={handleBulkExport}
        onBulkShare={handleBulkShare}
        onBulkVerify={handleBulkVerify}
        onBulkArchive={handleBulkArchive}
        onBulkDelete={handleBulkDelete}
        onCreateEvidencePackage={handleCreateEvidencePackage}
      />

      {/* Artifacts Table */}
      <ArtifactsTable
        artifacts={filteredArtifacts}
        selectedArtifacts={selectedArtifacts}
        onSelectionChange={setSelectedArtifacts}
        sort={sort}
        onSortChange={setSort}
        onViewArtifact={handleViewArtifact}
        onDownloadArtifact={handleDownloadArtifact}
        onVerifyArtifact={handleVerifyArtifact}
      />

      {/* Results Summary */}
      <div className="text-sm text-muted-foreground text-center">
        Showing {filteredArtifacts.length} audit-ready evidence artifacts
      </div>
    </div>
  )
}
