"use client"

import * as React from "react"
import { Archive, Shield, Alert<PERSON><PERSON>gle, CheckCircle, Clock, HardDrive } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { StorageMetrics } from "@/lib/artifacts-types"
import { formatFileSize } from "@/lib/artifacts-data"

interface ArtifactsMetricsProps {
  metrics: StorageMetrics
  className?: string
}

export function ArtifactsMetrics({ metrics, className }: ArtifactsMetricsProps) {
  const verificationRate = Math.round((metrics.verifiedCount / metrics.totalArtifacts) * 100)
  const failureRate = Math.round((metrics.failedVerification / metrics.totalArtifacts) * 100)
  
  return (
    <div className={`grid gap-4 md:grid-cols-2 lg:grid-cols-4 ${className}`}>
      {/* Total Artifacts */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Artifacts</CardTitle>
          <Archive className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{metrics.totalArtifacts.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            {formatFileSize(metrics.totalSize)} total storage
          </p>
        </CardContent>
      </Card>

      {/* Verification Status */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Verification Status</CardTitle>
          <Shield className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">{verificationRate}%</div>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <CheckCircle className="h-3 w-3 text-green-500" />
            {metrics.verifiedCount} verified
          </div>
        </CardContent>
      </Card>

      {/* Pending Verification */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Pending Verification</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-yellow-600">{metrics.pendingVerification}</div>
          <p className="text-xs text-muted-foreground">
            Awaiting blockchain verification
          </p>
        </CardContent>
      </Card>

      {/* Storage Usage */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Storage Usage</CardTitle>
          <HardDrive className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{metrics.storageUsagePercent}%</div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${metrics.storageUsagePercent}%` }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Verification Health Overview */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="text-sm font-medium">Verification Health</CardTitle>
          <CardDescription>Blockchain verification status breakdown</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-1">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">Verified</span>
              </div>
              <div className="text-lg font-bold text-green-600">{metrics.verifiedCount}</div>
              <div className="text-xs text-muted-foreground">{verificationRate}%</div>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-1">
                <Clock className="h-4 w-4 text-yellow-500" />
                <span className="text-sm font-medium">Pending</span>
              </div>
              <div className="text-lg font-bold text-yellow-600">{metrics.pendingVerification}</div>
              <div className="text-xs text-muted-foreground">
                {Math.round((metrics.pendingVerification / metrics.totalArtifacts) * 100)}%
              </div>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-1">
                <AlertTriangle className="h-4 w-4 text-red-500" />
                <span className="text-sm font-medium">Failed</span>
              </div>
              <div className="text-lg font-bold text-red-600">{metrics.failedVerification}</div>
              <div className="text-xs text-muted-foreground">{failureRate}%</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Top Artifact Types */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle className="text-sm font-medium">Artifact Distribution</CardTitle>
          <CardDescription>Most common artifact types</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Object.entries(metrics.artifactsByType)
              .sort(([,a], [,b]) => b - a)
              .slice(0, 5)
              .map(([type, count]) => (
                <div key={type} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {type.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Badge>
                  </div>
                  <div className="text-sm font-medium">{count}</div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>

      {/* Storage Health Indicators */}
      {(metrics.storageUsagePercent > 80 || failureRate > 10) && (
        <Card className="md:col-span-2 lg:col-span-4 border-yellow-200 dark:border-yellow-800">
          <CardHeader>
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
              Storage Health Alerts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {metrics.storageUsagePercent > 80 && (
                <div className="flex items-center gap-2 text-sm">
                  <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                    High Storage Usage
                  </Badge>
                  <span className="text-muted-foreground">
                    Storage is {metrics.storageUsagePercent}% full. Consider archiving old artifacts.
                  </span>
                </div>
              )}
              
              {failureRate > 10 && (
                <div className="flex items-center gap-2 text-sm">
                  <Badge variant="outline" className="text-red-600 border-red-600">
                    High Verification Failure Rate
                  </Badge>
                  <span className="text-muted-foreground">
                    {failureRate}% of artifacts failed verification. Review blockchain connectivity.
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
