export type ArtifactType = 
  | "control-evidence"
  | "assessment-report" 
  | "policy-document"
  | "incident-report"
  | "audit-evidence"
  | "compliance-certificate"
  | "risk-assessment"
  | "security-log"
  | "configuration-backup"
  | "training-record"

export type SourceModule = 
  | "overview"
  | "activity" 
  | "assets"
  | "monitor"
  | "frameworks"
  | "controls"
  | "policies"
  | "assessments"
  | "workflows"
  | "remediation"
  | "reports"
  | "portals"
  | "manual-upload"

export type BlockchainStatus = "verified" | "pending" | "failed" | "not-verified"

export type AccessLevel = "public" | "internal" | "restricted" | "confidential"

export interface Artifact {
  id: string
  name: string
  description?: string
  artifactType: ArtifactType
  sourceModule: SourceModule
  createdAt: Date
  updatedAt: Date
  createdBy: string
  fileSize: number
  fileFormat: string
  downloadUrl: string
  blockchainStatus: BlockchainStatus
  blockchainHash?: string
  blockchainBlock?: string
  verificationTimestamp?: Date
  accessLevel: AccessLevel
  tags: string[]
  relatedControls: string[]
  relatedFrameworks: string[]
  retentionDate?: Date
  isArchived: boolean
  accessHistory: AccessRecord[]
  chainOfCustody: CustodyRecord[]
}

export interface AccessRecord {
  id: string
  userId: string
  userName: string
  action: "view" | "download" | "share" | "verify"
  timestamp: Date
  ipAddress?: string
  userAgent?: string
}

export interface CustodyRecord {
  id: string
  action: "created" | "modified" | "verified" | "archived" | "restored" | "shared"
  userId: string
  userName: string
  timestamp: Date
  details?: string
  previousHash?: string
  newHash?: string
}

export interface ArtifactFilter {
  search?: string
  artifactTypes?: ArtifactType[]
  sourceModules?: SourceModule[]
  blockchainStatus?: BlockchainStatus[]
  accessLevels?: AccessLevel[]
  dateRange?: {
    start: Date
    end: Date
  }
  tags?: string[]
  frameworks?: string[]
  isArchived?: boolean
}

export interface ArtifactSort {
  field: keyof Artifact
  direction: "asc" | "desc"
}

export interface StorageMetrics {
  totalArtifacts: number
  totalSize: number
  verifiedCount: number
  pendingVerification: number
  failedVerification: number
  storageUsagePercent: number
  averageFileSize: number
  artifactsByType: Record<ArtifactType, number>
  artifactsByModule: Record<SourceModule, number>
}

export interface VerificationResult {
  isValid: boolean
  blockchainHash: string
  blockchainBlock: string
  verificationTimestamp: Date
  details?: string
  error?: string
}
